import { getYouTubeVideoId } from '../lib/youtube-utils';

import { youtubeService } from './api/youtubeService';
import { fetchSingleVideoFromGoogleSearch } from '../../services/googleSearchService';
import {
  metadataNormalizationService,
  type UnifiedVideoMetadata,
  type UnifiedChannelMetadata,
} from './metadataNormalizationService';
import { googleSearchVideoStore } from '../../services/googleSearchVideoStore';

/**
 * Configuration for unified data fetching
 */
interface UnifiedDataConfig {
  sources: {
    local: boolean;
    youtube: boolean;
  };
  limits: {
    local?: number;
    youtube?: number;
    total?: number;
  };
  caching: {
    enabled: boolean;
    ttl: number;
  };
  mixing: {
    strategy: 'round-robin' | 'source-priority' | 'relevance';
    sourcePriority?: Array<'local' | 'youtube'>;
  };
}

const defaultConfig: UnifiedDataConfig = {
  sources: {
    local: false,
    youtube: true,
  },
  limits: {
    local: 0,
    youtube: 50,
    total: 50,
  },
  caching: {
    enabled: true,
    ttl: 10 * 60 * 1000, // 10 minutes
  },
  mixing: {
    strategy: 'source-priority',
    sourcePriority: ['youtube'],
  },
};

/**
 * Search filters for unified queries
 */
export interface UnifiedSearchFilters {
  query?: string;
  category?: string;
  duration?: 'short' | 'medium' | 'long';
  uploadDate?: 'hour' | 'today' | 'week' | 'month' | 'year';
  sortBy?: 'relevance' | 'date' | 'views' | 'rating';
  type?: 'video' | 'short' | 'live';
  sources?: Array<'local' | 'youtube'>;
}

/**
 * Response structure for unified data queries
 */
export interface UnifiedDataResponse<T> {
  data: T[];
  sources: {
    local: {
      count: number;
      hasMore: boolean;
    };
    youtube: {
      count: number;
      hasMore: boolean;
      nextPageToken?: string;
    };
  };
  totalCount: number;
  hasMore: boolean;
  pagination?: {
    page: number;
    limit: number;
    total: number;
  };
}

/**
 * Service for fetching and normalizing data from multiple sources
 */
class UnifiedDataService {
  private config: UnifiedDataConfig;
  private cache = new Map<string, { data: any; timestamp: number }>();

  constructor(config: Partial<UnifiedDataConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  /**
   * Fetch trending videos from all enabled sources
   */
  async getTrendingVideos(
    limit: number = 50,
    filters: UnifiedSearchFilters = {},
  ): Promise<UnifiedDataResponse<UnifiedVideoMetadata>> {
    const cacheKey = `trending:${JSON.stringify({ limit, filters })}`;
    const cached = this.getCachedData<UnifiedDataResponse<UnifiedVideoMetadata>>(cacheKey);

    if (cached) {
      return cached;
    }

    const sources = filters.sources || Object.keys(this.config.sources).filter(
      key => this.config.sources[key as keyof typeof this.config.sources],
    ) as Array<'local' | 'youtube'>;

    const results = await Promise.allSettled([
      ...(sources.includes('local') ? [this.fetchLocalTrendingVideos(filters)] : []),
      ...(sources.includes('youtube') ? [this.fetchYouTubeTrendingVideos(filters)] : []),
    ]);

    const localResult = sources.includes('local') ? results[0] : null;
    const youtubeResult = sources.includes('youtube') ? results[sources.includes('local') ? 1 : 0] : null;

    const localVideos = localResult?.status === 'fulfilled' ? localResult.value : [];
    const youtubeVideos = youtubeResult?.status === 'fulfilled' ? youtubeResult.value : [];

    // Mix videos according to strategy
    const mixedVideos = this.mixVideoResults(localVideos, youtubeVideos, limit);

    const response: UnifiedDataResponse<UnifiedVideoMetadata> = {
      data: mixedVideos.slice(0, limit),
      sources: {
        local: {
          count: localVideos.length,
          hasMore: localVideos.length >= (this.config.limits.local || 25),
        },
        youtube: {
          count: youtubeVideos.length,
          hasMore: youtubeVideos.length >= (this.config.limits.youtube || 25),
        },
      },
      totalCount: mixedVideos.length,
      hasMore: mixedVideos.length > limit,
    };

    this.setCachedData(cacheKey, response);
    return response;
  }

  /**
   * Search videos across all enabled sources
   */
  async searchVideos(
    query: string,
    filters: UnifiedSearchFilters = {},
    limit: number = 50,
  ): Promise<UnifiedDataResponse<UnifiedVideoMetadata>> {
    if (!query.trim()) {
      return this.getTrendingVideos(limit, filters);
    }

    const cacheKey = `search:${query}:${JSON.stringify({ filters, limit })}`;
    const cached = this.getCachedData<UnifiedDataResponse<UnifiedVideoMetadata>>(cacheKey);

    if (cached) {
      return cached;
    }

    const sources = filters.sources || Object.keys(this.config.sources).filter(
      key => this.config.sources[key as keyof typeof this.config.sources],
    ) as Array<'local' | 'youtube'>;

    const results = await Promise.allSettled([
      ...(sources.includes('local') ? [this.searchLocalVideos(query, filters)] : []),
      ...(sources.includes('youtube') ? [this.searchYouTubeVideos(query, filters)] : []),
    ]);

    const localResult = sources.includes('local') ? results[0] : null;
    const youtubeResult = sources.includes('youtube') ? results[sources.includes('local') ? 1 : 0] : null;

    const localVideos = localResult?.status === 'fulfilled' ? localResult.value : [];
    const youtubeVideos = youtubeResult?.status === 'fulfilled' ? youtubeResult.value : [];

    // Mix and rank results by relevance
    const mixedVideos = this.mixVideoResults(localVideos, youtubeVideos, limit);

    const response: UnifiedDataResponse<UnifiedVideoMetadata> = {
      data: mixedVideos.slice(0, limit),
      sources: {
        local: {
          count: localVideos.length,
          hasMore: false, // Local search typically returns all matches
        },
        youtube: {
          count: youtubeVideos.length,
          hasMore: youtubeVideos.length >= (this.config.limits.youtube || 25),
        },
      },
      totalCount: mixedVideos.length,
      hasMore: mixedVideos.length > limit,
    };

    this.setCachedData(cacheKey, response);
    return response;
  }

  /**
   * Check if ID is a YouTube video format and extract actual YouTube ID
   */
  private extractYouTubeId(id: string): string | null {
    // Handle youtube-prefixed IDs (e.g., youtube-YQHsXMglC9A)
    if (id.startsWith('youtube-')) {
      return id.substring(8); // Remove 'youtube-' prefix
    }

    // Handle google-search-prefixed IDs (e.g., google-search-YQHsXMglC9A)
    if (id.startsWith('google-search-')) {
      return id.substring(14); // Remove 'google-search-' prefix
    }

    // Handle URLs that might be passed as IDs
    const youtubeId = getYouTubeVideoId(id);
    if (youtubeId) {
      return youtubeId;
    }

    // Check if it's already a valid YouTube video ID (11 characters)
    if (id.length === 11 && /^[a-zA-Z0-9_-]+$/.test(id)) {
      return id;
    }

    return null;
  }

  /**
   * Get video by ID from any source
   */
  async getVideoById(id: string): Promise<UnifiedVideoMetadata | null> {
    console.log(`🚀 UnifiedDataService.getVideoById called with ID: ${id}`);
    const cacheKey = `video:${id}`;
    const cached = this.getCachedData<UnifiedVideoMetadata>(cacheKey);

    if (cached) {
      console.log(`✅ UnifiedDataService: Returning cached video for ID: ${id}`);
      return cached;
    }

    console.log(`🔍 UnifiedDataService: Getting video by ID: ${id}`);

    // Check if this is a Google Custom Search video first
    if (id.startsWith('google-search-')) {
      console.log(`🔍 Detected Google Custom Search video ID: ${id}`);
      console.log(`🔍 Checking googleSearchVideoStore for video: ${id}`);
      const googleSearchVideo = googleSearchVideoStore.getVideo(id);
      console.log(`🔍 googleSearchVideoStore.getVideo result:`, googleSearchVideo);
      
      if (googleSearchVideo) {
        console.log(`✅ Found Google Custom Search video in store: ${googleSearchVideo.title}`);
        console.log('📊 Google Custom Search metadata:', {
          id: googleSearchVideo.id,
          title: googleSearchVideo.title,
          channelName: googleSearchVideo.channelName,
          channelAvatarUrl: googleSearchVideo.channelAvatarUrl,
          views: googleSearchVideo.viewCount,
          source: 'Google Custom Search JSON API'
        });

        // Convert Google Custom Search result to unified format
        const normalized: UnifiedVideoMetadata = {
          id: googleSearchVideo.id,
          title: googleSearchVideo.title,
          description: googleSearchVideo.description || '',
          thumbnailUrl: googleSearchVideo.thumbnailUrl || '',
          videoUrl: googleSearchVideo.videoUrl || `https://www.youtube.com/watch?v=${googleSearchVideo.id.replace('google-search-', '')}`,
          views: googleSearchVideo.viewCount || 0,
          viewsFormatted: this.formatViews(googleSearchVideo.viewCount || 0),
          likes: googleSearchVideo.likeCount || 0,
          dislikes: googleSearchVideo.dislikeCount || 0,
          commentCount: googleSearchVideo.commentCount || 0,
          channel: {
            id: googleSearchVideo.channelId || '',
            name: googleSearchVideo.channelName || 'YouTube Channel',
            avatarUrl: googleSearchVideo.channelAvatarUrl || '',
            subscribers: 0,
            subscribersFormatted: '0 subscribers',
            isVerified: googleSearchVideo.channelName?.includes('VEVO') || googleSearchVideo.channelName?.includes('Official') || false,
          },
          duration: googleSearchVideo.duration || '0:00',
          publishedAt: googleSearchVideo.uploadedAt || new Date().toISOString(),
          publishedAtFormatted: this.formatTimeAgo(googleSearchVideo.uploadedAt || new Date().toISOString()),
          category: googleSearchVideo.categoryId || 'General',
          tags: googleSearchVideo.tags || [],
          isLive: false,
          isShort: false,
          visibility: 'public' as const,
          source: 'external' as const,
          metadata: {
            quality: 'hd',
            definition: 'high'
          },
        };

        // Cache the result
        this.setCachedData(cacheKey, normalized);
        return normalized;
      } else {
        console.log(`❌ Google Custom Search video not found in store: ${id}`);
        
        // Try to fetch the video directly from Google Custom Search API
        console.log(`🌐 Attempting to fetch video directly from Google Custom Search API`);
        const youtubeId = id.replace('google-search-', '');
        console.log(`📋 Extracted YouTube ID: ${youtubeId}`);
        
        try {
          console.log(`🔄 Calling fetchSingleVideoFromGoogleSearch with ID: ${youtubeId}`);
          console.log(`🔄 About to call fetchSingleVideoFromGoogleSearch function...`);
          const googleSearchVideo = await fetchSingleVideoFromGoogleSearch(youtubeId);
          console.log(`🔄 fetchSingleVideoFromGoogleSearch returned:`, googleSearchVideo);
          if (googleSearchVideo) {
            console.log(`✅ Successfully fetched video from Google Custom Search API: ${googleSearchVideo.title}`);
            
            // Convert to unified format
            const normalized: UnifiedVideoMetadata = {
              id: googleSearchVideo.id,
              title: googleSearchVideo.title,
              description: googleSearchVideo.description || '',
              thumbnailUrl: googleSearchVideo.thumbnailUrl || '',
              videoUrl: googleSearchVideo.videoUrl || `https://www.youtube.com/watch?v=${youtubeId}`,
              views: googleSearchVideo.viewCount || 0,
              viewsFormatted: this.formatViews(googleSearchVideo.viewCount || 0),
              likes: googleSearchVideo.likeCount || 0,
              dislikes: googleSearchVideo.dislikeCount || 0,
              commentCount: googleSearchVideo.commentCount || 0,
              channel: {
                id: googleSearchVideo.channelId || '',
                name: googleSearchVideo.channelName || 'YouTube Channel',
                avatarUrl: googleSearchVideo.channelAvatarUrl || '',
                subscribers: 0,
                subscribersFormatted: '0 subscribers',
                isVerified: googleSearchVideo.channelName?.includes('VEVO') || googleSearchVideo.channelName?.includes('Official') || false,
              },
              duration: googleSearchVideo.duration || '0:00',
              publishedAt: googleSearchVideo.uploadedAt || new Date().toISOString(),
              publishedAtFormatted: this.formatTimeAgo(googleSearchVideo.uploadedAt || new Date().toISOString()),
              category: googleSearchVideo.categoryId || 'General',
              tags: googleSearchVideo.tags || [],
              isLive: false,
              isShort: false,
              visibility: 'public' as const,
              source: 'external' as const,
              metadata: {
                quality: 'hd',
                definition: 'high'
              },
            };

            // Cache the result
            this.setCachedData(cacheKey, normalized);
            return normalized;
          }
        } catch (error) {
          console.error('❌ Failed to fetch video from Google Custom Search API:', error);
          console.error('Error details:', {
            message: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            videoId: youtubeId,
            originalId: id
          });
        }
        
        // If Google Custom Search API fails, continue to YouTube API as fallback
        console.log(`🔄 Continuing to YouTube API as fallback for: ${id}`);
      }
    }

    // Check if this is a YouTube video ID
    const youtubeId = this.extractYouTubeId(id);

    console.log(`UnifiedDataService: Extracted YouTube ID: ${youtubeId} from ${id}`);

    if (youtubeId) {
      // This is a YouTube video, try YouTube first
      console.log(`Detected YouTube video ID: ${youtubeId}`);
      if (this.config.sources.youtube) {
        try {
          console.log(`Fetching YouTube video with ID: ${youtubeId}`);
          const youtubeVideos = await youtubeService.fetchVideos([youtubeId]);
          if (youtubeVideos.length > 0) {
            const video = youtubeVideos[0];
            console.log('Successfully fetched YouTube video:', video);
            if (video) {
              console.log('Video metadata details:', {
                id: video.id,
                title: video.title,
                channelName: video.channelName,
                channelAvatarUrl: video.channelAvatarUrl,
                views: video.viewCount,
                source: 'YouTube Data API v3'
              });
            }
            // Convert already processed YouTube video to unified format
            const processedVideo = video;
            if (!processedVideo) {
              return null;
            }
            const normalized: UnifiedVideoMetadata = {
              id: processedVideo.id,
              title: processedVideo.title,
              description: processedVideo.description,
              thumbnailUrl: processedVideo.thumbnailUrl,
              videoUrl: processedVideo.videoUrl,
              views: processedVideo.viewCount || 0,
              viewsFormatted: this.formatViews(processedVideo.viewCount || 0),
              likes: processedVideo.likeCount || 0,
              dislikes: processedVideo.dislikeCount || 0,
              commentCount: processedVideo.commentCount || 0,
              channel: {
                id: processedVideo.channelId,
                name: processedVideo.channelName,
                avatarUrl: processedVideo.channelAvatarUrl || processedVideo.channel?.avatarUrl || '',
                subscribers: 0, // Will be fetched separately if needed
                subscribersFormatted: '0 subscribers',
                isVerified: processedVideo.channel?.isVerified || false,
              },
              duration: processedVideo.duration,
              publishedAt: processedVideo.publishedAt || new Date().toISOString(),
              publishedAtFormatted: this.formatTimeAgo(processedVideo.publishedAt || new Date().toISOString()),
              category: processedVideo.category,
              tags: processedVideo.tags,
              isLive: processedVideo.isLive || false,
              isShort: processedVideo.isShort || false,
              visibility: processedVideo.visibility,
              source: 'youtube',
              metadata: {
                quality: 'hd',
                definition: 'hd',
                captions: false,
                language: 'en',
                license: 'youtube',
              },
            };
            this.setCachedData(cacheKey, normalized);
            return normalized;
          }
        } catch (error) {
          console.warn('Failed to fetch YouTube video:', error);
        }
      }
    } else {
      // For non-YouTube IDs, try YouTube search as fallback
      if (this.config.sources.youtube) {
        try {
          const youtubeVideos = await youtubeService.fetchVideos([id]);
          if (youtubeVideos.length > 0) {
            // Convert already processed YouTube video to unified format
            const processedVideo = youtubeVideos[0];
            if (!processedVideo) {
              return null;
            }
            const normalized: UnifiedVideoMetadata = {
              id: processedVideo.id,
              title: processedVideo.title,
              description: processedVideo.description,
              thumbnailUrl: processedVideo.thumbnailUrl,
              videoUrl: processedVideo.videoUrl,
              views: processedVideo.viewCount || 0,
              viewsFormatted: this.formatViews(processedVideo.viewCount || 0),
              likes: processedVideo.likeCount || 0,
              dislikes: processedVideo.dislikeCount || 0,
              commentCount: processedVideo.commentCount || 0,
              channel: {
                id: processedVideo.channelId,
                name: processedVideo.channelName,
                avatarUrl: processedVideo.channelAvatarUrl || '',
                subscribers: 0,
                subscribersFormatted: '0 subscribers',
                isVerified: processedVideo.channel?.isVerified || false,
              },
              duration: processedVideo.duration,
              publishedAt: processedVideo.publishedAt || new Date().toISOString(),
              publishedAtFormatted: this.formatTimeAgo(processedVideo.publishedAt || new Date().toISOString()),
              category: processedVideo.category,
              tags: processedVideo.tags,
              isLive: processedVideo.isLive || false,
              isShort: processedVideo.isShort || false,
              visibility: processedVideo.visibility,
              source: 'youtube',
              metadata: {
                quality: 'hd',
                definition: 'hd',
                captions: false,
                language: 'en',
                license: 'youtube',
              },
            };
            this.setCachedData(cacheKey, normalized);
            return normalized;
          }
        } catch (error) {
          console.warn('Failed to fetch YouTube video as fallback:', error);
        }
      }
    }

    console.warn(`No video found for ID: ${id}`);
    return null;
  }

  /**
   * Get channel by ID from any source
   */
  async getChannelById(id: string): Promise<UnifiedChannelMetadata | null> {
    const cacheKey = `channel:${id}`;
    const cached = this.getCachedData<UnifiedChannelMetadata>(cacheKey);

    if (cached) {
      return cached;
    }

    // Try YouTube
    if (this.config.sources.youtube) {
      try {
        const youtubeChannel = await youtubeService.fetchChannel(id);
        if (youtubeChannel) {
          const normalized = metadataNormalizationService.normalizeYouTubeChannel(youtubeChannel);
          this.setCachedData(cacheKey, normalized);
          return normalized;
        }
      } catch (error) {
        console.warn('Failed to fetch YouTube channel:', error);
      }
    }

    return null;
  }

  /**
   * Get shorts videos from all sources
   */
  async getShortsVideos(limit: number = 30): Promise<UnifiedDataResponse<UnifiedVideoMetadata>> {
    const filters: UnifiedSearchFilters = { type: 'short' };
    return this.getTrendingVideos(limit, filters);
  }

  // Private methods for fetching from specific sources

  private async fetchLocalTrendingVideos(_filters: UnifiedSearchFilters): Promise<UnifiedVideoMetadata[]> {
    // Local trending videos disabled - returning empty array
    return [];
  }

  private async fetchYouTubeTrendingVideos(filters: UnifiedSearchFilters): Promise<UnifiedVideoMetadata[]> {
    try {
      // Note: This is a placeholder - YouTube API trending would need different implementation
      // For now, we'll use search with popular terms
      const trendingQueries = ['trending', 'popular', 'viral', 'latest'];
      const randomQuery = trendingQueries[Math.floor(Math.random() * trendingQueries.length)];

      if (randomQuery) {
        return this.searchYouTubeVideos(randomQuery, filters);
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch YouTube trending videos:', error);
      return [];
    }
  }

  private async searchLocalVideos(query: string, filters: UnifiedSearchFilters): Promise<UnifiedVideoMetadata[]> {
    // Local video search disabled - returning empty array
    console.log('Local video search disabled for query:', query, filters);
    return [];
  }

  private async searchYouTubeVideos(query: string, filters: UnifiedSearchFilters): Promise<UnifiedVideoMetadata[]> {
    try {
      // This would need YouTube Search API implementation
      // For now, returning empty array as placeholder
      console.log('YouTube search not implemented for query:', query, filters);
      return [];
    } catch (error) {
      console.error('Failed to search YouTube videos:', error);
      return [];
    }
  }

  // Video mixing strategies

  private mixVideoResults(
    localVideos: UnifiedVideoMetadata[],
    youtubeVideos: UnifiedVideoMetadata[],
    limit: number,
  ): UnifiedVideoMetadata[] {
    switch (this.config.mixing.strategy) {
      case 'round-robin':
        return this.roundRobinMix(localVideos, youtubeVideos, limit);
      case 'source-priority':
        return this.sourcePriorityMix(localVideos, youtubeVideos, limit);
      case 'relevance':
        return this.relevanceMix(localVideos, youtubeVideos, limit);
      default:
        return this.roundRobinMix(localVideos, youtubeVideos, limit);
    }
  }

  private roundRobinMix(
    localVideos: UnifiedVideoMetadata[],
    youtubeVideos: UnifiedVideoMetadata[],
    limit: number,
  ): UnifiedVideoMetadata[] {
    const mixed: UnifiedVideoMetadata[] = [];
    const maxLength = Math.max(localVideos.length, youtubeVideos.length);

    for (let i = 0; i < maxLength && mixed.length < limit; i++) {
      if (i < localVideos.length && localVideos[i]) {
        mixed.push(localVideos[i]!);
      }
      if (i < youtubeVideos.length && mixed.length < limit && youtubeVideos[i]) {
        mixed.push(youtubeVideos[i]!);
      }
    }

    return mixed;
  }

  private sourcePriorityMix(
    localVideos: UnifiedVideoMetadata[],
    youtubeVideos: UnifiedVideoMetadata[],
    limit: number,
  ): UnifiedVideoMetadata[] {
    const priority = this.config.mixing.sourcePriority || ['local', 'youtube'];
    const mixed: UnifiedVideoMetadata[] = [];

    for (const source of priority) {
      const videos = source === 'local' ? localVideos : youtubeVideos;
      const remainingLimit = limit - mixed.length;
      mixed.push(...videos.slice(0, remainingLimit));

      if (mixed.length >= limit) {
        break;
      }
    }

    return mixed;
  }

  private relevanceMix(
    localVideos: UnifiedVideoMetadata[],
    youtubeVideos: UnifiedVideoMetadata[],
    limit: number,
  ): UnifiedVideoMetadata[] {
    // Combine all videos and sort by relevance (views, likes, recency)
    const allVideos = [...localVideos, ...youtubeVideos];

    allVideos.sort((a, b) => {
      // Simple relevance scoring based on views and engagement
      const scoreA = a.views + (a.likes * 10) + (a.commentCount * 5);
      const scoreB = b.views + (b.likes * 10) + (b.commentCount * 5);
      return scoreB - scoreA;
    });

    return allVideos.slice(0, limit);
  }

  // Cache management

  private getCachedData<T>(key: string): T | null {
    if (!this.config.caching.enabled) {
      return null;
    }

    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }

    const isExpired = Date.now() - cached.timestamp > this.config.caching.ttl;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return cached.data as T;
  }

  private setCachedData(key: string, data: any): void {
    if (!this.config.caching.enabled) {
      return;
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Clear cache for specific pattern or all
   */
  clearCache(pattern?: string): void {
    if (!pattern) {
      this.cache.clear();
      return;
    }

    const regex = new RegExp(pattern);
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<UnifiedDataConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): UnifiedDataConfig {
    return { ...this.config };
  }

  // Utility methods

  private formatViews(count: number): string {
    if (count >= 1000000000) {
      return `${(count / 1000000000).toFixed(1)}B views`;
    }
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M views`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K views`;
    }
    return `${count} views`;
  }

  private formatTimeAgo(dateString: string): string {
    if (!dateString) {
      return '';
    }

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();

    const seconds = Math.floor(diffMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const weeks = Math.floor(days / 7);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    if (years > 0) {
      return `${years} year${years > 1 ? 's' : ''} ago`;
    }
    if (months > 0) {
      return `${months} month${months > 1 ? 's' : ''} ago`;
    }
    if (weeks > 0) {
      return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
    }
    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
    if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    }
    if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    }
    return 'Just now';
  }
}

// Export singleton instance
export const unifiedDataService = new UnifiedDataService();
export default unifiedDataService;
