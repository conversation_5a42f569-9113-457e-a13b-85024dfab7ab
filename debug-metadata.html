<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Metadata Fetch</title>
</head>
<body>
    <h1>Debug Metadata Fetch</h1>
    <button onclick="testMetadataFetch()">Test Metadata Fetch</button>
    <div id="results"></div>

    <script type="module">
        async function testMetadataFetch() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = 'Testing metadata fetch...';
            
            try {
                // Test the Google Custom Search API directly
                const searchApiKey = 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ';
                const searchEngineId = '61201925358ea4e83';
                const youtubeVideoId = 'bnVUHWCynig';
                
                const searchQuery = `site:youtube.com/watch ${youtubeVideoId}`;
                const searchUrl = new URL('https://www.googleapis.com/customsearch/v1');
                searchUrl.searchParams.set('key', searchApiKey);
                searchUrl.searchParams.set('cx', searchEngineId);
                searchUrl.searchParams.set('q', searchQuery);
                searchUrl.searchParams.set('num', '1');
                
                console.log('🔍 Testing Google Custom Search API...');
                console.log('Search URL:', searchUrl.toString());
                
                const response = await fetch(searchUrl.toString());
                console.log('📡 Response status:', response.status, response.statusText);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ API Error:', errorText);
                    resultsDiv.innerHTML = `<pre style="color: red;">API Error: ${response.status} ${response.statusText}\n${errorText}</pre>`;
                    return;
                }
                
                const data = await response.json();
                console.log('📥 API Response:', data);
                
                if (data.error) {
                    resultsDiv.innerHTML = `<pre style="color: red;">Error: ${JSON.stringify(data.error, null, 2)}</pre>`;
                } else if (data.items && data.items.length > 0) {
                    const item = data.items[0];
                    
                    // Convert to our format
                    const result = {
                        id: `google-search-${youtubeVideoId}`,
                        title: item.title || 'YouTube Video',
                        description: item.snippet || '',
                        thumbnailUrl: item.pagemap?.cse_thumbnail?.[0]?.src || item.pagemap?.cse_image?.[0]?.src || '',
                        videoUrl: item.link,
                        embedUrl: `https://www.youtube.com/embed/${youtubeVideoId}`,
                        uploadedAt: new Date().toISOString(),
                        channelName: 'YouTube Channel',
                        channelId: `channel-${youtubeVideoId}`,
                        duration: 'Unknown',
                        viewCount: Math.floor(Math.random() * 1000000),
                        likeCount: Math.floor(Math.random() * 10000),
                        dislikeCount: Math.floor(Math.random() * 1000),
                        commentCount: Math.floor(Math.random() * 5000),
                        categoryId: 'General',
                        tags: [],
                        isYouTube: true,
                        source: 'google-search',
                    };
                    
                    console.log('✅ Converted result:', result);
                    
                    resultsDiv.innerHTML = `
                        <div style="color: green;">
                            <h3>✅ Success! Metadata fetched from Google Custom Search API</h3>
                            <p><strong>Title:</strong> ${result.title}</p>
                            <p><strong>Description:</strong> ${result.description}</p>
                            <p><strong>Video URL:</strong> ${result.videoUrl}</p>
                            <p><strong>Thumbnail:</strong> <img src="${result.thumbnailUrl}" style="max-width: 200px;"></p>
                            <p><strong>Channel:</strong> ${result.channelName}</p>
                            <p><strong>Views:</strong> ${result.viewCount.toLocaleString()}</p>
                        </div>
                        <details>
                            <summary>Raw API Response</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                        <details>
                            <summary>Converted Result</summary>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    resultsDiv.innerHTML = `<pre style="color: orange;">No results found for video ID: ${youtubeVideoId}</pre>`;
                }
            } catch (error) {
                console.error('❌ Error:', error);
                resultsDiv.innerHTML = `<pre style="color: red;">Error: ${error.message}</pre>`;
            }
        }
        
        // Make function available globally
        window.testMetadataFetch = testMetadataFetch;
    </script>
</body>
</html>
